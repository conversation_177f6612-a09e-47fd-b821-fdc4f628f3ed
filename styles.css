/* CSS变量定义 */
:root {
    /* 色彩系统 */
    --color-black: #000000;
    --color-dark-gray: #1a1a1a;
    --color-medium-gray: #666666;
    --color-light-gray: #cccccc;
    --color-gold: #d4af37;
    --color-dark-blue: #1e3a5f;
    --color-white: #ffffff;
    
    /* 字体大小 */
    --font-size-title-main: 28px;
    --font-size-title-secondary: 24px;
    --font-size-title-tertiary: 20px;
    --font-size-body: 16px;
    --font-size-small: 14px;
    --font-size-tiny: 12px;
    
    /* 间距系统 */
    --spacing-xs: 8px;
    --spacing-sm: 16px;
    --spacing-md: 24px;
    --spacing-lg: 32px;
    --spacing-xl: 48px;
    --spacing-xxl: 64px;
    
    /* 其他 */
    --border-radius: 8px;
    --border-radius-small: 4px;
    --shadow: 0 4px 16px rgba(0,0,0,0.3);
}

/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'SimSun', serif;
    background: linear-gradient(180deg, var(--color-dark-gray) 0%, var(--color-black) 100%);
    color: var(--color-white);
    line-height: 1.6;
}

/* 容器 */
.container {
    width: 540px;
    margin: 0 auto;
    background: linear-gradient(180deg, var(--color-dark-gray) 0%, var(--color-black) 100%);
}

/* 通用标题样式 */
.section-title {
    font-family: 'Inter', 'SimHei', sans-serif;
    font-weight: 700;
    font-size: var(--font-size-title-secondary);
    color: var(--color-white);
    margin-bottom: var(--spacing-lg);
    text-align: center;
}

/* 店铺介绍 */
.shop-intro {
    position: relative;
    height: 270px;
    overflow: hidden;
}

.shop-intro__background {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--color-dark-blue) 0%, var(--color-black) 100%);
    position: relative;
}

.shop-intro__overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
}

.shop-intro__content {
    padding: var(--spacing-md);
    text-align: center;
    max-width: 480px;
}

.shop-intro__title {
    font-family: 'Inter', 'SimHei', sans-serif;
    font-weight: 700;
    font-size: var(--font-size-title-main);
    color: var(--color-white);
    margin-bottom: var(--spacing-sm);
}

.shop-intro__description {
    font-size: var(--font-size-body);
    color: var(--color-white);
    margin-bottom: var(--spacing-sm);
}

.shop-intro__location {
    margin-top: var(--spacing-md);
}

.shop-intro__find {
    font-family: 'Inter', sans-serif;
    font-weight: 300;
    font-size: var(--font-size-small);
    color: var(--color-gold);
    margin-bottom: var(--spacing-xs);
}

.shop-intro__address {
    font-size: var(--font-size-body);
    color: var(--color-gold);
    font-weight: bold;
}

/* 创始人介绍 */
.founder {
    padding: var(--spacing-xl) var(--spacing-md);
}

.founder__content {
    display: flex;
    gap: var(--spacing-md);
    align-items: flex-start;
}

.founder__photo {
    flex: 0 0 120px;
}

.founder__photo img {
    width: 100%;
    height: 240px;
    object-fit: cover;
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--shadow);
}

.founder__info {
    flex: 1;
    padding-left: var(--spacing-sm);
}

.founder__name {
    font-family: 'Inter', 'SimHei', sans-serif;
    font-weight: 700;
    font-size: var(--font-size-title-secondary);
    color: var(--color-white);
    margin-bottom: var(--spacing-sm);
}

.founder__achievements {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.founder__title {
    font-size: var(--font-size-small);
    color: var(--color-gold);
    font-weight: bold;
}

.founder__award {
    font-size: var(--font-size-small);
    color: var(--color-light-gray);
}

/* 剧目介绍 */
.shows {
    padding: var(--spacing-xl) var(--spacing-md);
}

.show-item {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
    align-items: flex-start;
}

.show-item__poster {
    flex: 0 0 120px;
}

.show-item__poster img {
    width: 100%;
    height: 240px;
    object-fit: cover;
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--shadow);
}

.show-item__info {
    flex: 1;
    padding-left: var(--spacing-sm);
}

.show-item__title {
    font-family: 'Inter', 'SimHei', sans-serif;
    font-weight: 700;
    font-size: var(--font-size-title-tertiary);
    color: var(--color-white);
    margin-bottom: var(--spacing-sm);
}

.show-item__performer,
.show-item__duration {
    font-size: var(--font-size-small);
    color: var(--color-gold);
    margin-bottom: var(--spacing-xs);
}

.show-item__description {
    font-size: var(--font-size-small);
    color: var(--color-light-gray);
    line-height: 1.6;
}

/* Master of Magic特殊布局 */
.show-item--master {
    flex-direction: column;
}

.master-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 4px;
    margin-bottom: var(--spacing-md);
    width: 100%;
}

.master-grid img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: var(--border-radius-small);
}

/* 本月排期 */
.schedule {
    padding: var(--spacing-xl) var(--spacing-md);
}

.calendar {
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.calendar__header {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.calendar__day {
    text-align: center;
    font-family: 'Inter', 'SimHei', sans-serif;
    font-weight: 700;
    font-size: var(--font-size-small);
    color: var(--color-gold);
    padding: var(--spacing-xs);
}

.calendar__row {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.calendar__row:last-child {
    margin-bottom: 0;
}

.calendar__cell {
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-small);
    padding: var(--spacing-xs);
    min-height: 120px;
}

.calendar__date {
    font-family: 'Inter', sans-serif;
    font-weight: 700;
    font-size: 18px;
    color: var(--color-white);
    text-align: center;
    margin-bottom: var(--spacing-xs);
}

.calendar__slot {
    margin-bottom: var(--spacing-xs);
    padding: 4px 8px;
    border-radius: var(--border-radius-small);
    text-align: center;
}

.calendar__slot--li {
    background: var(--color-black);
    color: var(--color-white);
}

.calendar__slot--ye {
    background: var(--color-medium-gray);
    color: var(--color-white);
}

.calendar__slot--artists {
    background: var(--color-white);
    color: var(--color-black);
}

.calendar__time {
    font-family: 'Inter', sans-serif;
    font-size: var(--font-size-tiny);
    margin-bottom: 2px;
}

.calendar__show {
    font-size: var(--font-size-tiny);
    font-weight: bold;
}

/* 观众评价 */
.reviews {
    padding: var(--spacing-xl) var(--spacing-md);
}

.review-item {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    align-items: flex-start;
}

.review-item__avatar {
    flex: 0 0 60px;
}

.review-item__avatar img {
    width: 60px;
    height: 80px;
    object-fit: cover;
    border-radius: var(--border-radius-small);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.review-item__content {
    flex: 1;
}

.review-item__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xs);
}

.review-item__name {
    font-family: 'Inter', 'SimHei', sans-serif;
    font-weight: 700;
    font-size: var(--font-size-body);
    color: var(--color-white);
}

.review-item__rating {
    color: var(--color-gold);
    font-size: var(--font-size-small);
}

.review-item__text {
    font-size: var(--font-size-small);
    color: var(--color-light-gray);
    line-height: 1.5;
}

/* 观演须知 */
.notice {
    padding: var(--spacing-xl) var(--spacing-md);
}

.notice__list {
    list-style: none;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.notice__item {
    font-size: var(--font-size-small);
    color: var(--color-light-gray);
    margin-bottom: var(--spacing-sm);
    padding-left: var(--spacing-sm);
    position: relative;
}

.notice__item:last-child {
    margin-bottom: 0;
}

.notice__item::before {
    content: "•";
    color: var(--color-gold);
    position: absolute;
    left: 0;
}

/* 特别鸣谢 */
.thanks {
    padding: var(--spacing-xl) var(--spacing-md) var(--spacing-xxl);
}

.thanks__grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-sm) var(--spacing-md);
    text-align: center;
}

.thanks__name {
    font-size: var(--font-size-small);
    color: var(--color-light-gray);
    padding: var(--spacing-xs);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-small);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 响应式调整 */
@media (max-width: 540px) {
    .container {
        width: 100%;
    }
}
