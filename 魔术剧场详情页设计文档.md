# 謎·魔术酒吧详情页设计文档

## 目录

1. [项目概述](#项目概述)
2. [设计原则](#设计原则)
3. [视觉规范](#视觉规范)
4. [界面设计规范](#界面设计规范)
5. [功能模块设计](#功能模块设计)
6. [技术实现规范](#技术实现规范)

---

## 项目概述

### 项目背景
基于《謎·魔术酒吧详情页需求文档》，设计一个静态手机端魔术剧场详情页面，最终渲染为图片格式用于购票平台展示。

### 设计目标
- 传达神秘、优雅、现代的品牌调性
- 完整展示酒吧及演出信息
- 营造沉浸式的视觉体验
- 确保信息层次清晰易读

---

## 设计原则

### 视觉原则
- **神秘感**：运用深色调和光影效果营造神秘氛围
- **优雅性**：采用简约布局和精致细节体现品质
- **现代感**：使用当代设计语言和排版方式

### 信息原则
- **层次分明**：通过视觉权重引导阅读顺序
- **内容完整**：确保所有关键信息准确传达
- **易于理解**：采用直观的信息组织方式

---

## 视觉规范

### 色彩系统

#### 主色调
- **主黑色**：`#000000` - 用于标题和重要文字
- **深灰色**：`#1a1a1a` - 用于背景和分割
- **中灰色**：`#666666` - 用于次要文字
- **浅灰色**：`#cccccc` - 用于辅助信息

#### 辅助色彩
- **金色强调**：`#d4af37` - 用于特殊标记和装饰
- **深蓝色**：`#1e3a5f` - 用于李柘翰剧目背景
- **纯白色**：`#ffffff` - 用于Artists of Magic背景

### 字体规范

#### 中文字体
- **标题字体**：思源黑体 Bold
  - 主标题：28px
  - 二级标题：24px
  - 三级标题：20px
- **正文字体**：宋体 Regular
  - 正文：16px
  - 辅助文字：14px
  - 说明文字：12px

#### 英文字体
- **标题**：Inter Bold
- **正文**：Inter Regular
- **装饰文字**：Inter Light

### 间距系统
- **基础单位**：8px
- **小间距**：8px、16px
- **中间距**：24px、32px
- **大间距**：48px、64px

---

## 界面设计规范

### 整体布局
- **页面宽度**：540px（固定）
- **内容边距**：左右各24px
- **模块间距**：48px
- **背景色**：深灰渐变 `linear-gradient(180deg, #1a1a1a 0%, #000000 100%)`

### 模块设计规范

#### 通用组件规范
- **圆角半径**：8px（卡片）、4px（按钮）
- **阴影效果**：`0 4px 16px rgba(0,0,0,0.3)`
- **边框**：1px solid rgba(255,255,255,0.1)

#### 图片规范
- **背景图片**：2:1 比例，最小分辨率 1080×540px
- **人物照片**：1:2 比例，最小分辨率 270×540px
- **剧目海报**：1:2 比例，最小分辨率 270×540px
- **用户头像**：3:4 比例，最小分辨率 60×80px

---

## 功能模块设计

### 1. 店铺介绍模块

#### 布局结构
```
┌─────────────────────────────────────┐
│           背景图片 (2:1)              │
│  ┌─────────────────────────────┐    │
│  │        文字叠加区域           │    │
│  │                             │    │
│  └─────────────────────────────┘    │
└─────────────────────────────────────┘
```

#### 设计要点
- 背景图片添加深色遮罩（opacity: 0.4）
- 文字使用白色，确保可读性
- 主标题使用思源黑体 Bold 28px
- 正文使用宋体 Regular 16px
- 地址信息使用金色强调

### 2. 创始人介绍模块

#### 布局结构
```
┌─────────┬─────────────────────────┐
│         │  李柘翰                  │
│  人物    │  謎·魔术酒吧 创始人       │
│  照片    │  湖北高校魔术联盟 创始人   │
│ (1:2)   │  2009年 全民大魔竞...    │
│         │  2012年 湖北首届...      │
└─────────┴─────────────────────────┘
```

#### 设计要点
- 人物照片左对齐，占据1/3宽度
- 右侧信息区域采用垂直排列
- 姓名使用思源黑体 Bold 24px
- 职位和成就使用宋体 Regular 14px
- 添加微妙的分割线区分不同信息

### 3. 剧目介绍模块

#### 常规剧目布局
```
┌─────────┬─────────────────────────┐
│         │  剧目名称                │
│  海报    │  表演者：XXX             │
│ (1:2)   │  演出时长：XX分钟         │
│         │  简介：...              │
└─────────┴─────────────────────────┘
```

#### Master of Magic特殊布局
```
┌───┬───┬───┐
│ 1 │ 2 │ 3 │
├───┼───┼───┤
│ 4 │ 5 │ 6 │
├───┼───┼───┤
│ 7 │ 8 │ 9 │
└───┴───┴───┘
```

#### 设计要点
- 海报图片添加微妙边框和阴影
- 剧目名称使用思源黑体 Bold 20px
- 表演者和时长信息使用金色强调
- 简介文字使用宋体 Regular 14px，行高1.6

### 4. 本月排期模块

#### 日历布局设计
```
┌─────────┬─────────┬─────────┐
│   周三   │   周五   │   周六   │
├─────────┼─────────┼─────────┤
│    3    │    5    │    6    │
│         │  20:00  │  20:00  │
│         │  绘遇见  │   轨道   │
│  22:00  │  22:00  │  22:00  │
│  魔术师  │  魔术师  │  魔术师  │
├─────────┼─────────┼─────────┤
│   10    │   12    │   13    │
│         │  20:00  │  20:00  │
│         │  绘遇见  │   宿命   │
│  22:00  │  22:00  │  22:00  │
│  魔术师  │  魔术师  │  魔术师  │
└─────────┴─────────┴─────────┘
```

#### 颜色编码系统
- **李柘翰剧目**：黑色背景 `#000000`，白色文字
- **叶雄剧目**：灰色背景 `#666666`，白色文字  
- **Artists of Magic**：白色背景 `#ffffff`，黑色文字

#### 设计要点
- 日期数字使用思源黑体 Bold 18px
- 时间使用Inter Regular 12px
- 剧目名称使用宋体 Regular 12px
- 每个时段格子高度40px，确保信息清晰

### 5. 观众评价模块

#### 单条评价布局
```
┌─────┬─────────────────────────────┐
│     │  用户名 ⭐⭐⭐⭐⭐            │
│头像 │                             │
│(3:4)│  评价内容...                │
│     │                             │
└─────┴─────────────────────────────┘
```

#### 设计要点
- 头像圆角处理，添加细边框
- 用户名使用思源黑体 Bold 16px
- 星级评价使用金色 `#d4af37`
- 评价文字使用宋体 Regular 14px，行高1.5
- 每条评价间距32px

### 6. 观演须知模块

#### 设计要点
- 使用列表形式展示
- 每条须知前添加装饰性图标
- 文字使用宋体 Regular 14px
- 重要信息（如年龄限制）使用金色强调

### 7. 特别鸣谢模块

#### 布局设计
- 采用网格布局，每行2-3个名字
- 名字间使用装饰性分隔符
- 整体居中对齐

#### 设计要点
- 标题使用思源黑体 Bold 20px
- 名字使用宋体 Regular 14px
- 添加微妙的背景纹理增强视觉层次

---

## 技术实现规范

### HTML结构
- 使用语义化标签组织内容
- 合理使用section、article、header等标签
- 确保结构清晰，便于样式控制

### CSS规范
- 采用BEM命名规范
- 使用CSS Grid和Flexbox进行布局
- 合理使用CSS变量管理颜色和尺寸
- 确保在540px宽度下完美显示

### 图片优化
- 使用高质量图片素材
- 合理压缩确保清晰度
- 添加适当的图片预处理效果

### 导出规范
- 最终导出为PNG格式
- 分辨率不低于1080×1920px
- 确保文字清晰可读
- 保持色彩准确性

---

*本设计文档基于需求文档制定，如有调整需求请及时沟通修改。*
